import Navigation from '@/components/Navigation'
import Link from 'next/link'
import { Tutorial } from '@/types'
import { notFound } from 'next/navigation'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import { TutorialDataAccess } from '@/lib/data-access'

async function getTutorial(id: string): Promise<Tutorial | null> {
  try {
    return await TutorialDataAccess.getById(id)
  } catch (error) {
    console.error('Error fetching tutorial:', error)
    return null
  }
}

async function getAllTutorials(): Promise<Tutorial[]> {
  try {
    return await TutorialDataAccess.getAll()
  } catch (error) {
    console.error('Error fetching tutorials:', error)
    return []
  }
}



interface TutorialDetailPageProps {
  params: {
    id: string
  }
}

export default async function TutorialDetailPage({ params }: TutorialDetailPageProps) {
  const [tutorial, allTutorials] = await Promise.all([
    getTutorial(params.id),
    getAllTutorials()
  ])

  if (!tutorial) {
    notFound()
  }

  // Create a more detailed content from the tutorial description
  const content = `# ${tutorial.title}

${tutorial.description}

## Course Overview

This comprehensive tutorial provides in-depth instruction on advanced magical techniques. Perfect for serious practitioners looking to elevate their craft.

### What You'll Learn

- Professional-level techniques and methods
- Detailed explanations of core principles
- Practical applications and performance tips
- Advanced variations and combinations

### Course Features

- **High-definition video instruction** with multiple camera angles
- **Detailed practice exercises** to develop your skills
- **Professional insights** from experienced performers
- **Lifetime access** to all content and future updates

### Prerequisites

This course is designed for magicians with some experience in the fundamentals. Basic knowledge of magical principles is recommended.

### Investment in Your Craft

At $${tutorial.price}, this represents exceptional value for professional-level instruction that would cost significantly more in private lessons.`

  return (
    <div className="min-h-screen bg-cream-50 text-navy-900">
      <Navigation />
      
      {/* Breadcrumb */}
      <section className="pt-32 pb-8">
        <div className="container-max">
          <nav className="flex items-center space-x-2 text-sm font-body">
            <Link href="/" className="text-navy-600 hover:text-burgundy-700 transition-colors">
              Home
            </Link>
            <span className="text-navy-400">→</span>
            <Link href="/tutorials" className="text-navy-600 hover:text-burgundy-700 transition-colors">
              Tutorials
            </Link>
            <span className="text-navy-400">→</span>
            <span className="text-burgundy-700 font-medium">{tutorial.title}</span>
          </nav>
        </div>
      </section>

      {/* Tutorial Header */}
      <section className="pb-16">
        <div className="container-max">
          <div className="grid lg:grid-cols-2 gap-12 items-start">
            {/* Tutorial Preview */}
            <div className="relative">
              <div className="aspect-video bg-navy-100 rounded-lg overflow-hidden shadow-2xl">
                <div className="w-full h-full bg-gradient-to-br from-burgundy-100 to-navy-100 flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-20 h-20 bg-burgundy-700 rounded-full flex items-center justify-center mx-auto mb-6 hover:bg-burgundy-800 transition-colors cursor-pointer">
                      <svg className="w-8 h-8 text-cream-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <p className="text-navy-600 font-body text-lg">Watch Preview</p>
                  </div>
                </div>
              </div>
              
              {/* Video List Preview */}
              {tutorial.videos.length > 0 && (
                <div className="mt-6 bg-cream-100 rounded-lg p-4">
                  <h4 className="font-display font-medium text-navy-800 mb-3">Course Content</h4>
                  <div className="space-y-2">
                    {tutorial.videos.slice(0, 3).map((video) => (
                      <div key={video.id} className="flex items-center text-sm text-navy-600">
                        <svg className="w-4 h-4 mr-2 text-burgundy-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span className="flex-grow">{video.title}</span>
                        <span className="text-navy-500">{Math.floor(video.duration / 60)}min</span>
                      </div>
                    ))}
                    {tutorial.videos.length > 3 && (
                      <div className="text-sm text-navy-500 italic">
                        +{tutorial.videos.length - 3} more videos
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Tutorial Info */}
            <div>
              <div className="classical-border mb-8">
                <h1 className="text-4xl md:text-5xl font-display font-semibold mb-4 text-navy-900 text-shadow">
                  {tutorial.title}
                </h1>
                <p className="text-xl text-navy-700 font-body leading-relaxed">
                  {tutorial.description}
                </p>
              </div>

              {/* Pricing & Purchase */}
              <div className="card mb-8">
                <div className="flex items-center justify-between mb-6">
                  <div className="text-4xl font-display font-semibold text-burgundy-700">
                    ${tutorial.price}
                  </div>
                  <div className="text-right">
                    <div className="text-navy-600 font-body text-sm mb-1">One-time purchase</div>
                    <div className="text-navy-600 font-body text-sm">Lifetime access</div>
                  </div>
                </div>

                <Link href={`/purchase/${tutorial.id}`} className="btn-primary w-full text-lg mb-4 text-center block">
                  Purchase Tutorial
                </Link>

                <div className="text-center text-navy-600 font-body text-sm">
                  Secure payment via PayPal • 30-day money-back guarantee
                </div>
              </div>

              {/* Tutorial Stats */}
              <div className="grid grid-cols-2 gap-4 mb-8">
                <div className="text-center p-4 bg-cream-100 rounded-lg">
                  <div className="text-2xl font-display font-semibold text-burgundy-700 mb-1">
                    {tutorial.duration ? `${Math.floor(tutorial.duration / 3600)}h ${Math.floor((tutorial.duration % 3600) / 60)}m` : '2.5h'}
                  </div>
                  <div className="text-navy-600 font-body text-sm">Total Duration</div>
                </div>
                <div className="text-center p-4 bg-cream-100 rounded-lg">
                  <div className="text-2xl font-display font-semibold text-burgundy-700 mb-1">
                    {tutorial.videos.length || 6}
                  </div>
                  <div className="text-navy-600 font-body text-sm">Video Lessons</div>
                </div>
              </div>

              {/* Features */}
              <div className="space-y-3">
                <div className="flex items-center text-navy-700 font-body">
                  <svg className="w-5 h-5 mr-3 text-burgundy-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  Lifetime access to all content
                </div>
                <div className="flex items-center text-navy-700 font-body">
                  <svg className="w-5 h-5 mr-3 text-burgundy-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  High-definition video instruction
                </div>
                <div className="flex items-center text-navy-700 font-body">
                  <svg className="w-5 h-5 mr-3 text-burgundy-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  Downloadable practice materials
                </div>
                <div className="flex items-center text-navy-700 font-body">
                  <svg className="w-5 h-5 mr-3 text-burgundy-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  30-day money-back guarantee
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Tutorial Content */}
      <section className="section-padding bg-cream-100 paper-texture">
        <div className="container-max">
          <div className="max-w-4xl mx-auto">
            <article className="prose prose-lg prose-navy max-w-none">
              <ReactMarkdown 
                remarkPlugins={[remarkGfm]}
                components={{
                  h1: ({children}) => (
                    <h1 className="text-4xl md:text-5xl font-display font-semibold mb-8 text-navy-900 text-shadow border-b-2 border-gold-200 pb-4">
                      {children}
                    </h1>
                  ),
                  h2: ({children}) => (
                    <h2 className="text-3xl md:text-4xl font-display font-medium mb-6 mt-12 text-burgundy-700 relative">
                      <span className="text-gold-600 mr-3">❦</span>
                      {children}
                    </h2>
                  ),
                  h3: ({children}) => (
                    <h3 className="text-2xl md:text-3xl font-display font-medium mb-4 mt-8 text-navy-800">
                      {children}
                    </h3>
                  ),
                  p: ({children}) => (
                    <p className="text-lg text-navy-800 font-body leading-relaxed mb-6">
                      {children}
                    </p>
                  ),
                  blockquote: ({children}) => (
                    <blockquote className="border-l-4 border-gold-500 pl-6 py-4 my-8 bg-cream-200 rounded-r-lg italic text-lg text-navy-700 font-display">
                      {children}
                    </blockquote>
                  ),
                  ul: ({children}) => (
                    <ul className="list-none space-y-3 mb-6 text-lg text-navy-800 font-body">
                      {children}
                    </ul>
                  ),
                  ol: ({children}) => (
                    <ol className="list-decimal list-inside space-y-3 mb-6 text-lg text-navy-800 font-body pl-4">
                      {children}
                    </ol>
                  ),
                  li: ({children}) => (
                    <li className="flex items-start">
                      <span className="text-burgundy-600 mr-3 mt-1">•</span>
                      <span>{children}</span>
                    </li>
                  ),
                  strong: ({children}) => (
                    <strong className="font-semibold text-burgundy-700">
                      {children}
                    </strong>
                  ),
                  em: ({children}) => (
                    <em className="italic text-navy-700 font-display">
                      {children}
                    </em>
                  )
                }}
              >
                {content}
              </ReactMarkdown>
            </article>
          </div>
        </div>
      </section>

      {/* Related Tutorials */}
      <section className="section-padding">
        <div className="container-max">
          <div className="classical-border mb-12 text-center">
            <h2 className="text-3xl md:text-4xl font-display font-medium text-navy-900 mb-4">
              Related Tutorials
            </h2>
            <p className="text-navy-600 font-body text-lg max-w-2xl mx-auto">
              Continue your magical education with these complementary courses
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {allTutorials
              .filter((relatedTutorial: Tutorial) => relatedTutorial.id !== tutorial.id)
              .slice(0, 2)
              .map((relatedTutorial: Tutorial) => (
                <Link
                  key={relatedTutorial.id}
                  href={`/tutorials/${relatedTutorial.id}`}
                  className="group block"
                >
                  <div className="card hover-lift group-hover:shadow-2xl transition-all duration-300">
                    <h3 className="text-xl font-display font-medium mb-3 text-burgundy-700 group-hover:text-burgundy-800 transition-colors">
                      {relatedTutorial.title}
                    </h3>
                    <p className="text-navy-700 font-body leading-relaxed mb-4">
                      {relatedTutorial.description.substring(0, 120)}...
                    </p>
                    <div className="flex items-center justify-between">
                      <span className="text-xl font-display font-semibold text-burgundy-700">
                        ${relatedTutorial.price}
                      </span>
                      <span className="text-burgundy-600 font-display text-sm group-hover:text-burgundy-800 transition-colors">
                        View Tutorial →
                      </span>
                    </div>
                  </div>
                </Link>
              ))}
          </div>
        </div>
      </section>

      {/* Classical Footer */}
      <footer className="section-padding bg-navy-900 border-t-4 border-gold-600 relative">
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-10 left-10 text-4xl text-gold-400">❦</div>
          <div className="absolute top-10 right-10 text-4xl text-gold-400 rotate-180">❦</div>
          <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 text-6xl text-cream-200">◆</div>
        </div>

        <div className="container-max text-center relative z-10">
          <div className="section-divider mb-12"></div>
          <div className="flex items-center justify-center mb-6">
            <span className="text-gold-400 text-2xl mr-3">❦</span>
            <div className="text-3xl font-display font-semibold text-cream-100 tracking-wide">
              Magic Academy
            </div>
            <span className="text-gold-400 text-2xl ml-3 rotate-180">❦</span>
          </div>
          <p className="text-navy-300 font-body text-lg mb-8 max-w-2xl mx-auto italic">
            "Preserving the classical traditions of magical artistry for future generations,
            where timeless elegance meets the wonder of the impossible."
          </p>
          <div className="flex flex-wrap justify-center gap-4 sm:gap-8 mb-8">
            <Link href="/" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Home</Link>
            <Link href="/portfolio" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Portfolio</Link>
            <Link href="/blog" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Blog</Link>
            <Link href="/tutorials" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Tutorials</Link>
          </div>
          <div className="border-t border-navy-700 pt-8">
            <p className="text-navy-400 font-body">
              © 2025 Magic Academy. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
